import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorApplicationStatusPage extends BasePage {
    // Main page locators
    private readonly mentorApplicationStatusHeading: Locator;
    private readonly refreshButton: Locator;

    // Page field locators
    private readonly applicationStatusField: Locator;
    private readonly applicationDetailsField: Locator;
    private readonly supportingDocumentsField: Locator;

    constructor(page: Page) {
        super(page);
        this.mentorApplicationStatusHeading = this.page.getByText('Mentor Application Status');
        this.refreshButton = this.page.getByRole('button', { name: 'Refresh' });

        // Page field locators
        this.applicationStatusField = this.page.getByText('Application Status', { exact: true });
        this.applicationDetailsField = this.page.getByText('Application Details');
        this.supportingDocumentsField = this.page.getByText('Supporting Documents', { exact: true });
    }

    async verifyOnMentorApplicationStatusPage(): Promise<void> {
        await expect(this.mentorApplicationStatusHeading).toBeVisible();
    }

    async clickMentorApplicationStatusHeading(): Promise<void> {
        await this.mentorApplicationStatusHeading.click();
    }

    async clickRefreshButton(): Promise<void> {
        await this.refreshButton.click();
    }

    async verifyMentorFullName(expectedName: string): Promise<void> {
        const nameHeading = this.page.getByRole('heading', { name: expectedName });
        await expect(nameHeading).toBeVisible();
        await nameHeading.click();
    }

    async verifyMentorEmail(expectedEmail: string): Promise<void> {
        const emailText = this.page.getByText(expectedEmail);
        await expect(emailText).toBeVisible();
        await emailText.click();
    }

    async verifyMentorDataAfterRefresh(fullName: string, email: string): Promise<void> {
        await this.verifyMentorFullName(fullName);
        await this.verifyMentorEmail(email);
    }

    async verifyApplicationStatusField(): Promise<void> {
        await expect(this.applicationStatusField).toBeVisible();
        await this.applicationStatusField.click();
    }

    async verifyApplicationDetailsField(): Promise<void> {
        await expect(this.applicationDetailsField).toBeVisible();
        await this.applicationDetailsField.click();
    }

    async verifySupportingDocumentsField(): Promise<void> {
        await expect(this.supportingDocumentsField).toBeVisible();
        await this.supportingDocumentsField.click();
    }

    async verifyAllPageFields(): Promise<void> {
        await this.verifyApplicationStatusField();
        await this.verifyApplicationDetailsField();
        await this.verifySupportingDocumentsField();
    }
}
