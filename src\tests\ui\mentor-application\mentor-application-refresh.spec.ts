import { test, expect } from '@playwright/test';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApplicationStatusPage } from '../../../pages/mentor-application/mentor-application-status-page';
import testData from '../../../tests-data/mentor-application-tracking-data.json';
import { MentorApplicationTrackingTestData } from '../../../data-type/mentor-application-tracking.type';

// Configure test to run on Chromium only
test.describe('Mentor Application Tracking Tests', () => {
    test.describe.configure({ mode: 'parallel' });

    let loginPage: LoginPage;
    let mentorApplicationStatusPage: MentorApplicationStatusPage;
    const trackingData: MentorApplicationTrackingTestData = testData as MentorApplicationTrackingTestData;

    test.beforeEach(async ({ page, browserName }) => {
        // Skip test if not running on Chromium
        test.skip(browserName !== 'chromium', 'This test only runs on Chromium');

        loginPage = new LoginPage(page);
        mentorApplicationStatusPage = new MentorApplicationStatusPage(page);
    });

    test('@MentorApplicationTracking Refresh test - Verify mentor data after refresh', async ({ page }) => {
        const mentorCredentials = trackingData.presubmittedApplicationMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Click sign in, input email password, click sign in - login as presubmitted application mentor from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                mentorCredentials.email,
                mentorCredentials.password
            );
            await loginPage.clickOnSignInButton();

            // Verify login success by checking for 'Mentor Platform' link
            await page.waitForLoadState('networkidle');
            const mentorPlatformLink = page.getByRole('link', { name: 'Mentor Platform' });
            await expect(mentorPlatformLink).toBeVisible();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Click refresh button', async () => {
            await mentorApplicationStatusPage.clickRefreshButton();
        });

        await test.step('Check result: Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorDataAfterRefresh(
                mentorCredentials.fullName,
                mentorCredentials.email
            );
        });
    });
});
